part of editing_book_select;

class CardEditListPage extends GetView<CardEditListController> {
  const CardEditListPage({Key? key}) : super(key: key);

  AppBar _buildAppBar() {
    return AppBar(
      titleSpacing: 0,
      title: GetBuilder<CardEditListController>(
          init: CardEditListController(),
          builder: (controller) {
            return SinputSearch(
              hintText: LocaleKeys.formSearchCards.tr,
              controller: controller.textEditingController,
              onChanged: controller.onInputValueChanged,
            );
          }),
      actions: [
        IconButton(
          icon: Icon(
            Icons.add_circle_rounded,
            size: IconSize.titleXL,
            color: AppTheme.primary,
          ),
          onPressed: controller.toCardEditPage,
        )
      ],
    );
  }

  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        const SizedBox(height: 16),
        Expanded(child: _buildCardList()),
      ],
    );
  }

  Widget _buildCardList() {
    return GetBuilder<CardEditListController>(
        init: CardEditListController(),
        builder: (controller) {
          return SmartRefresher(
              controller: controller.refreshController,
              onRefresh: controller.onRefresh,
              onLoading: controller.onLoading,
              enablePullUp: true,
              enablePullDown: true,
              child: ListView.builder(
                  itemCount: controller.refreshDataList.length,
                  itemBuilder: (BuildContext context, int index) {
                    if (index > controller.refreshDataList.length) {
                      return const SizedBox();
                    }
                    return _buildCardItem(controller, index);
                  }));
        });
  }

  Widget _buildCardItem(CardEditListController controller, int index) {
    return Slidable(
        endActionPane: _buildSlideButton(controller, index),
        child: ListTile(
          title: Text(
            // fontSize: FontSize.title,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.bold,
            controller.refreshDataList[index].title,
          ),
          subtitle: Text(
            controller.refreshDataList[index].question,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          onTap: () => controller.toCardInfoPage(index),
          leading: Simage(
              width: 60,
              url: controller.getAssetUrl(CardAssetType.primaryImage,
                      controller.refreshDataList[index]) ??
                  ""),
        ));
  }

  ActionPane _buildSlideButton(CardEditListController controller, int index) {
    return ActionPane(
      motion: const ScrollMotion(),
      children: [
        SlidableAction(
          onPressed: (BuildContext context) => controller.toCardEditPage(
              cardModel: controller.refreshDataList[index]),
          backgroundColor: AppTheme.primary,
          foregroundColor: Colors.white,
          label: '编辑',
          // label: 'Archive',
        ),
        SlidableAction(
          onPressed: (context) {
            controller.onDeleteCard(controller.refreshDataList[index]);
          },
          backgroundColor: AppTheme.error,
          foregroundColor: Colors.white,
          label: '删除',
        ),
      ],
    );
  }

  // _buildCreateCardButtion(CardEditListController controller) {
  //   return FloatingActionButton(
  //     // backgroundColor: AppTheme.success,
  //     onPressed: controller.toCardEditPage,
  //     child: Icon(Icons.add, size: IconSize.title),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CardEditListController>(
      init: CardEditListController(),
      builder: (_) {
        return Scaffold(
          // floatingActionButton: _buildCreateCardButtion(controller),
          // floatingActionButtonLocation:
          //     FloatingActionButtonLocation.centerFloat,
          // resizeToAvoidBottomInset: true,
          appBar: _buildAppBar(),
          body: SafeArea(
            child: _buildView(),
          ),
        );
      },
    );
  }
}




// import 'package:cheestack_flt/common/data/index.dart';
// import 'package:cheestack_flt/common/enums/index.dart';
// import 'package:cheestack_flt/controllers/index.dart';
// import 'package:cheestack_flt/theme.dart';
// import 'package:cheestack_flt/widgets/index.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_slidable/flutter_slidable.dart';
// import 'package:get/get.dart';
// import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
// import 'index.dart';

// class CreationPage extends StatefulWidget {
//   const CreationPage({super.key});

//   @override
//   State<CreationPage> createState() => _CreationPageState();
// }

// class _CreationPageState extends State<CreationPage>
//     with AutomaticKeepAliveClientMixin {
//   String interstitialId = "";

//   @override
//   bool get wantKeepAlive => true;

//   @override
//   Widget build(BuildContext context) {
//     super.build(context);
//     return GetBuilder<EditingBookSelectController>(
//       init: EditingBookSelectController(),
//       builder: (controller) {
//         return GetBuilder<UserStore>(
//             init: UserStore(),
//             builder: (_) {
//               return Scaffold(
//                 floatingActionButton:
//                     AuthController.to.usr.config?.editingBookId == null
//                         ? null
//                         : FloatingActionButton(
//                             onPressed: () => controller.toCardEditPage(),
//                             child: Icon(Icons.add, size: IconSize.title),
//                           ),
//                 appBar: _buildAppBar(),
//                 body: SafeArea(
//                   minimum: const EdgeInsets.all(AppTheme.margin),
//                   child: AuthController.to.usr.config?.editingBookId == null
//                       ? _buildNoBookView()
//                       : _buildView(),
//                 ),
//               );
//             });
//       },
//     );
//   }


//   // 主视图
//   Widget _buildView() {
//     return Column(
//       children: [
//         _bookManageView(),
//         const SizedBox(height: 16),
//         Expanded(child: _buildCardList()),
//       ],
//     );
//   }

//   Widget _buildNoBookView() {
//     return GetBuilder<EditingBookSelectController>(
//         init: EditingBookSelectController(),
//         builder: (controller) {
//           return Center(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Stext("你还没有创建任何笔记本", fontSize: FontSize.title),
//                 const SizedBox(height: 16),
//                 Sbutton(
//                     size: SbuttonSize.small,
//                     onPressed: controller.toBookEditPage,
//                     child: const Stext("创建")),
//               ],
//             ),
//           );
//         });
//   }

//   _bookManageView() {
//     return GetBuilder<EditingBookSelectController>(
//         init: EditingBookSelectController(),
//         builder: (controller) {
//           return GetBuilder<UserStore>(
//               init: UserStore(),
//               builder: (userController) {
//                 if (userController.usr.config?.editingBook?.id == null) {
//                   return _buildBookSelectButton(controller, "选择编辑的笔记本");
//                 }
//                 return InkWell(
//                   onTap: controller.toBookInfoPage,
//                   child: Row(
//                     children: [
//                       const SizedBox(width: 16),
//                       Expanded(
//                         child: Row(
//                           children: [
//                             Icon(
//                               Icons.auto_stories,
//                               size: IconSize.title,
//                               color: AppTheme.primary,
//                             ),
//                             const SizedBox(width: 8),
//                             Expanded(
//                               child: Stext(
//                                 color: AppTheme.primary,
//                                 userController.usr.config?.editingBook?.name ??
//                                     "",
//                                 fontWeight: FontWeight.bold,
//                                 fontSize: FontSize.title,
//                                 overflow: TextOverflow.ellipsis,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       const SizedBox(width: 8),
//                       _buildBookSelectButton(controller, "管理"),
//                       const SizedBox(width: 8),
//                       _buildCreateBookButtion((controller)),
//                     ],
//                   ),
//                 );
//               });
//         });
//   }

//   Widget _buildCardList() {
//     return GetBuilder<EditingBookSelectController>(
//         init: EditingBookSelectController(),
//         builder: (controller) {
//           return SmartRefresher(
//               controller: controller.refreshController,
//               onRefresh: controller.onRefresh,
//               onLoading: controller.onLoading,
//               enablePullUp: true,
//               enablePullDown: true,
//               child: ListView.builder(
//                   itemCount: controller.refreshDataList.length,
//                   itemBuilder: (BuildContext context, int index) {
//                     if (index > controller.refreshDataList.length) {
//                       return const SizedBox();
//                     }

//                     return _buildCardItem(controller, index);
//                   }));
//         });
//   }

//   Widget _buildCardItem(EditingBookSelectController controller, int index) {
//     return Slidable(
//         endActionPane: _buildSlideButton(controller, index),
//         child: ListTile(
//           title: Stext(
//             // fontSize: FontSize.title,
//             fontWeight: FontWeight.bold,
//             controller.refreshDataList[index].title,
//           ),
//           subtitle: Stext(controller.refreshDataList[index].question),
//           onTap: () => controller.toCardInfoPage(index),
//           leading: Simage(
//               width: 60,
//               url: controller.getAssetUrl(CardAssetType.primaryImage,
//                       controller.refreshDataList[index]) ??
//                   ""),
//         ));
//   }

//   ActionPane _buildSlideButton(EditingBookSelectController controller, int index) {
//     return ActionPane(
//       motion: const ScrollMotion(),
//       children: [
//         SlidableAction(
//           onPressed: (BuildContext context) => controller.toCardEditPage(
//               cardModel: controller.refreshDataList[index]),
//           backgroundColor: AppTheme.primary,
//           foregroundColor: Colors.white,
//           icon: Icons.edit,
//           // label: 'Archive',
//         ),
//         SlidableAction(
//           onPressed: (context) {
//             controller.onDeleteCard(controller.refreshDataList[index]);
//           },
//           backgroundColor: AppTheme.warning,
//           foregroundColor: Colors.white,
//           icon: Icons.delete,
//           // label: 'Save',
//         ),
//       ],
//     );
//   }

//   Sbutton _buildBookSelectButton(EditingBookSelectController controller, String text) {
//     return Sbutton(
//       size: SbuttonSize.small,
//       onPressed: controller.toEditingBookSelect,
//       child: Stext(text),
//     );
//   }

//   _buildCreateBookButtion(EditingBookSelectController controller) {
//     return Sbutton(
//       size: SbuttonSize.small,
//       onPressed: controller.toBookEditPage,
//       child: const Stext("创建"),
//     );
//   }




  
// }
