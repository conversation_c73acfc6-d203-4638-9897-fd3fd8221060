part of card_editor;

class CardEditorPage extends StatefulWidget {
  const CardEditorPage({Key? key}) : super(key: key);

  @override
  State<CardEditorPage> createState() => _CardEditorPageState();
}

class _CardEditorPageState extends State<CardEditorPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  late CardEditorController controller;

  @override
  void initState() {
    super.initState();
    if (!Get.isRegistered<CardEditorController>()) {
      Get.lazyPut<CardEditorController>(() => CardEditorController());
    }
    controller = Get.find<CardEditorController>();
    controller.init();
    _updatePageView();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<CardEditorController>(
      init: CardEditorController(),
      builder: (_) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: _buildAppBar(),
          body: Safe<PERSON>rea(
            child: Stack(
              children: [
                _buildView(),
                // 浮动预览窗口
                if (controller.showLivePreview) _buildFloatingPreview(),
              ],
            ),
          ),
          floatingActionButton: _buildPreviewToggle(),
        );
      },
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: Text(
        'cardEditor'.tr,
        fontSize: FontSize.title,
      ),
    );
  }

  Widget _buildView() {
    return GetBuilder<CardEditorController>(builder: (controller) {
      return Column(
        children: [
          const SizedBox(height: 16),
          _buildPageIndicator(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildPageView(),
          ),
          const SizedBox(height: 16),
          _buildActionBar(),
        ],
      );
    });
  }

  SmoothPageIndicator _buildPageIndicator() {
    return SmoothPageIndicator(
      controller: controller.pageController,
      count: controller.pageViewList.length,
    );
  }

  Widget _buildPageView() {
    // 定义pageViewList
    return GetBuilder(
        init: CardEditorController(),
        builder: (controller) {
          return Form(
            key: controller.formKey,
            child: PageView(
              controller: controller.pageController,
              onPageChanged: controller.onPageChanged,
              children: controller.pageViewList,
            ),
          );
        });
  }

  /// infoPage
  Widget buildBaseInfoView() {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const MultiBookSelectSearchBottomSheet(),
          const SizedBox(height: 8),
          _buildCardTypeSelector(),
          const SizedBox(height: 8),
          _buildCardBaseInfo(), // title, // title
          // buildQstnImage(),
          // _buildNoteTextWidget(ANSWER_TEXT),
          // buildAnsImage(),
          // ansSelectorBtn(),
          // ..._buildNoteAudios(),
          // if (controller.state.editingNoteModel.id == null)
          //   _buildIsAddReview(),
          // SizedBox(height: screenSize4),
          // _buildActionBar(),
        ],
      ),
    );
  }

  Widget _buildCardTypeSelector() {
    /// 下拉框选项
    List<Map<String, dynamic>> items = controller.cardTypes;

    return GetBuilder<CardEditorController>(
        init: CardEditorController(),
        builder: (controller) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.all(8),
                child: const Text(
                  "卡片类型",
                  fontWeight: FontWeight.w600,
                ),
              ),
              DropdownButtonFormField(
                /// 初始值
                value: controller.cardType,
                alignment: AlignmentDirectional.centerStart,
                items: items
                    .map<DropdownMenuItem<CardType>>((e) => DropdownMenuItem(
                          value: e['value'],
                          child: Text(e['label']),
                        ))
                    .toList(),
                onChanged: controller.editType == EditType.create
                    ? (CardType? val) {
                        controller.cardType = val ?? CardType.general;
                        controller.setLastCardType();
                        _updatePageView();
                      }
                    : null,
              )
            ],
          );
        });
  }

  _updatePageView() {
    controller.pageViewList = [
      buildBaseInfoView().keepAlive(),
      buildCardFrontView().keepAlive(),
      if (controller.cardType != CardType.hanziWriter)
        _buildCardBackView().keepAlive(),
      // 添加预览页面
      _buildPreviewPage().keepAlive(),
    ];
    controller.update();
  }

  /// 构建预览页面
  Widget _buildPreviewPage() {
    return GetBuilder<CardEditorController>(
      init: CardEditorController(),
      builder: (controller) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '卡片预览',
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
              ),
              const SizedBox(height: 8),
              const Text(
                '这是您的卡片在学习时的显示效果',
                fontSize: 14.0,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),

              // 预览组件
              SizedBox(
                height: 400,
                child: SimpleCardPreview(
                  cardData: controller.cardModelCreate,
                  cardType: controller.cardType,
                ),
              ),

              const SizedBox(height: 24),

              // 提示信息
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue, size: 16),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '预览会根据您的输入实时更新。保存后，卡片将按此效果在学习模块中显示。',
                        fontSize: 12.0,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCardBaseInfo() {
    return OxInputField(
      controller: controller.baseInfoController,
      label: "标题",
      hintText: "知识点的介绍或关键字, 便于后期查找",
      maxLines: 4,
      onChanged: (value) {
        controller.updatePreviewData();
      },
    );
  }

  Widget _buildActionBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          child: Sbutton(
            shape: SbuttonShape.outline,
            onPressed: controller.pageIndex == 0
                ? null
                : () => controller.toNextPage(-1),
            child: const Text("上一页"),
          ),
        ),
        Expanded(
            child: controller.pageIndex == controller.pageViewList.length - 1
                ? Sbutton(
                    shape: SbuttonShape.outline,
                    backgroundColor: AppTheme.success,
                    child: const Text("完成"),
                    onPressed: () => controller.onSave(),
                  )
                : Sbutton(
                    shape: SbuttonShape.outline,
                    backgroundColor: AppTheme.success,
                    child: const Text("下一页"),
                    onPressed: () => controller.toNextPage(1),
                  ))
      ],
    );
  }

  Widget _buildCardBackView() {
    return GetBuilder<CardEditorController>(
        init: CardEditorController(),
        builder: (controller) {
          return Column(
            children: [
              buildAnswerText(),
              const SizedBox(height: 16),
              _buildAiVoiceView(isQuestion: false),
              const SizedBox(height: 16),
              _buildFileUploadField(page: PageType.back),
            ],
          );
        });
  }

  Widget _buildAddAttachment({PageType pageType = PageType.front}) {
    CardAssetType imageType = CardAssetType.primaryImage;
    CardAssetType audioType = CardAssetType.primaryAudio;
    if (pageType == PageType.back) {
      imageType = CardAssetType.secondaryImage;
      audioType = CardAssetType.secondaryAudio;
    }

    return Column(
      children: [
        ListTile(
          title: Text(
            "添加/变更图片",
            fontSize: FontSize.title,
          ),
          onTap: () => controller.toUploadImage(cardAssetType: imageType),
        ),
        ListTile(
          title: Text(
            "添加/变更音频",
            fontSize: FontSize.title,
          ),
          onTap: () => controller.toUploadAudio(cardAssetType: audioType),
        ),
      ],
    );
  }

  Widget buildCardFrontView() {
    return GetBuilder<CardEditorController>(
        init: CardEditorController(),
        builder: (controller) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 使用类型特定的编辑器
                TypeSpecificEditor(
                  cardType: controller.cardType,
                  controller: controller,
                ),
                const SizedBox(height: 24),

                // AI语音选项
                _buildAiVoiceView(isQuestion: true),
                const SizedBox(height: 16),

                // 文件上传
                _buildFileUploadField(),
              ],
            ),
          );
        });
  }

  Widget _buildFileUploadField({
    PageType page = PageType.front,
  }) {
    CardAssetType imageType = CardAssetType.primaryImage;
    String? imageUrl = controller.primaryImage.url;
    CardAssetType audioType = CardAssetType.primaryAudio;
    String? audioUrl = controller.primaryAudio.url;
    if (page == PageType.back) {
      imageType = CardAssetType.secondaryImage;
      imageUrl = controller.secondaryImage.url;
      audioType = CardAssetType.secondaryAudio;
      audioUrl = controller.secondaryAudio.url;
    }

    return GetBuilder<CardEditorController>(
        init: CardEditorController(),
        builder: (controller) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                  color: Colors.grey
                      .withValues(red: 128, green: 128, blue: 128, alpha: 0.4)),
            ),
            child: Column(
              children: [
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    if (imageUrl != null)
                      SimageWithDeleteButton(
                        image: imageUrl,
                        onDelete: () => controller.clearAsset(imageType),
                      ),
                    if (audioUrl != null)
                      SAudioWithDeleteButton(
                        onTap: () => controller.play(audioType),
                        audioPath: audioUrl,
                        onDelete: () => controller.clearAsset(audioType),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Sbutton(
                    size: SbuttonSize.small,
                    width: double.infinity,
                    onPressed: () {
                      CustomBottomSheet.show<String>(
                        context: Get.context!,
                        title: const Text('选择文件操作'),
                        builder: (context) =>
                            _buildAddAttachment(pageType: page),
                      );
                    },
                    child: const Text(
                      "添加文件",
                    )),
              ],
            ),
          );
        });
  }

  Widget buildQuestionText() {
    return GetBuilder<CardEditorController>(
        init: CardEditorController(),
        builder: (controller) {
          return OxInputField(
            controller: controller.frontTextController,
            label: "卡片文字",
            hintText: "输入卡片正面文字说明",
            maxLines: 3,
            onChanged: (value) {
              controller.updatePreviewData();
            },
          );
        });
  }

  Widget buildAnswerText() {
    return OxInputField(
      controller: controller.backTextController,
      label: "卡片文字",
      hintText: "输入卡片背面文字说明",
      maxLines: 3,
      onChanged: (value) {
        controller.updatePreviewData();
      },
    );
  }

  _buildAiVoiceView({bool isQuestion = true}) {
    return GetBuilder<CardEditorController>(
      init: CardEditorController(),
      builder: (controller) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text("使用AI语音(请勿包含特殊字符)", fontWeight: FontWeight.bold),
            CustomSwitch(
              value: isQuestion
                  ? controller.questionAiVoice
                  : controller.answerAiVoice,
              onChanged: (value) {
                if (isQuestion) {
                  controller.questionAiVoice = value;
                  if (value) {
                    controller.primaryAudio.url = null;
                  }
                } else {
                  controller.answerAiVoice = value;
                  if (value) {
                    controller.secondaryAudio.url = null;
                  }
                }
                controller.update();
                Console.log(controller.questionAiVoice);
                Console.log(controller.answerAiVoice);
              },
            ),
          ],
        );
      },
    );
  }

  /// 构建浮动预览窗口
  Widget _buildFloatingPreview() {
    return Positioned(
      right: 16,
      top: 16,
      child: Container(
        width: 300,
        height: 400,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // 预览标题栏
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.visibility, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '实时预览',
                      fontSize: 12.0,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),
                  GestureDetector(
                    onTap: controller.toggleLivePreview,
                    child:
                        const Icon(Icons.close, size: 16, color: Colors.grey),
                  ),
                ],
              ),
            ),

            // 预览内容
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: SimpleCardPreview(
                  cardData: controller.cardModelCreate,
                  cardType: controller.cardType,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预览切换按钮
  Widget _buildPreviewToggle() {
    return FloatingActionButton(
      mini: true,
      onPressed: controller.toggleLivePreview,
      backgroundColor: controller.showLivePreview ? Colors.blue : Colors.grey,
      child: Icon(
        controller.showLivePreview ? Icons.visibility_off : Icons.visibility,
        color: Colors.white,
      ),
    );
  }
}
