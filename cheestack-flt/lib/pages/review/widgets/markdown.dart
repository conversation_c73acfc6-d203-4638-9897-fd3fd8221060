import 'package:cheestack_flt/common/enums/index.dart';
import 'package:cheestack_flt/controllers/index.dart';
import 'package:cheestack_flt/pages/review/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:get/get.dart';

class MarkDownView extends StatefulWidget {
  final int pageIndex;
  const MarkDownView({
    super.key,
    required this.pageIndex,
  });

  @override
  State<MarkDownView> createState() => _MarkDownViewState();
}

class _MarkDownViewState extends State<MarkDownView> {
  late ReviewController controller;
  String text = "";
  String audioUrl = "";

  @override
  void initState() {
    // get controller
    if (!Get.isRegistered<ReviewController>()) {
      Get.lazyPut<ReviewController>(() => ReviewController());
    }
    controller = Get.find<ReviewController>();

    if (widget.pageIndex == 0) {
      text = controller.curCard.question ?? "";
      audioUrl = controller.getAssetUrl(CardAssetType.primaryAudio) ?? "";
      if (audioUrl.isNotEmpty) {
        /// 等待组件加载完成后播放音频
        WidgetsBinding.instance.addPostFrameCallback((_) {
          OxAudioController.to.play(audioUrl);
        });
      }
    } else if (widget.pageIndex == 1) {
      text = controller.curCard.answer ?? "";
      audioUrl = controller.getAssetUrl(CardAssetType.secondaryAudio) ?? "";
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
        init: ReviewController(),
        builder: (controller) {
          return Column(
            children: [
              Expanded(
                  child: Markdown(
                styleSheet: MarkdownStyleSheet(
                  a: const TextStyle(color: Colors.blue),
                  p: const TextStyle(color: Colors.red),
                  h1: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.bold,
                  ),
                  h2: const TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                selectable: true,
                data: text,
              )),
              const SizedBox(height: 16),
              if (audioUrl.isNotEmpty)
                Column(
                  children: [
                    SplayWithSpeed(
                      onPlayPressed: () => OxAudioController.to.play(audioUrl),
                      onSpeedPlayPressed: () =>
                          OxAudioController.to.play(audioUrl, speed: 0.7),
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              _buildActionBar(),
            ],
          );
        });
  }

  _buildActionBar() {
    if (widget.pageIndex == 0) {
      return Sbutton(
          shape: SbuttonShape.outline,
          width: double.infinity,
          size: SbuttonSize.large,
          onPressed: controller.toPageIndex,
          child: const Text("查看答案"));
    } else {
      return Row(
        children: [
          Expanded(
              child: Sbutton(
                  shape: SbuttonShape.outline,
                  width: double.infinity,
                  size: SbuttonSize.large,
                  onPressed: () => controller.toPageIndex(isCorrect: false),
                  child: const Text("忘记"))),
          Expanded(
              child: Sbutton(
                  shape: SbuttonShape.outline,
                  backgroundColor: AppTheme.success,
                  size: SbuttonSize.large,
                  width: double.infinity,
                  onPressed: () => controller.toPageIndex(isCorrect: true),
                  child: const Text("记得"))),
        ],
      );
    }
  }
}
