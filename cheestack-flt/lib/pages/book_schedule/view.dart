part of book_schedule;

class BookSchedulePage extends GetView<BookScheduleController> {
  const BookSchedulePage({Key? key}) : super(key: key);

  Widget _buildView() {
    return LayoutBuilder(builder: (context, constraints) {
      return SingleChildScrollView(
        child: ConstrainedBox(
            constraints: BoxConstraints(minHeight: constraints.biggest.height),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildActiveSwitch(),
                const SizedBox(height: 16),
                Text(
                  "当前学习数量${controller.bookSchedule.totalStudiedCard ?? 0} / ${controller.bookSchedule.totalCard ?? 0}",
                  fontSize: FontSize.title,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 16),
                Text(
                  "预计还需${controller.calcRestDays()}天学完",
                  fontSize: FontSize.titleXL,
                  color: AppTheme.success,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "计划每天学习: ",
                      // "计划每天学习${controller.bookSchedule.studyQty}",
                      fontSize: FontSize.title,
                      color: AppTheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                    SizedBox(
                      width: 90, // 调整框体大小
                      height: 50, // 调整框体高度
                      child: TextField(
                        controller: controller.studyQtyController,
                        textAlign: TextAlign.center,
                        textAlignVertical: TextAlignVertical.center, // 使文本垂直居中
                        keyboardType: TextInputType.number,
                        maxLength: 3,
                        style: TextStyle(
                          fontSize: FontSize.title,
                          color: AppTheme.primary,
                        ),
                        decoration: const InputDecoration(
                          counterText: '', // 隐藏长度提示
                        ),
                        onChanged: (value) {
                          if (value.isNotEmpty && int.parse(value) >= 1) {
                            controller.onChangeStudyQty(int.parse(value));
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SfSlider(
                  value: controller.bookSchedule.studyQty ?? 10,
                  min: 1.0,
                  max: 30.0,
                  interval: 10,
                  showTicks: true,
                  showLabels: true,
                  enableTooltip: true,
                  inactiveColor: Colors.blue[200],
                  activeColor: AppTheme.primary,
                  minorTicksPerInterval: 1,
                  onChanged: (value) {
                    // double转int
                    controller.onChangeStudyQty(value.toInt());
                  },
                ),
                const SizedBox(height: 16),
                _buildStudyFirstSwitch(),
                const SizedBox(height: 16),

              ],
            )),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookScheduleController>(
      init: BookScheduleController(),
      builder: (_) {
        return Scaffold(
          appBar: AppBar(title: const Text("学习计划")),
          body: SafeArea(
            child: SloadStateWrapper(
              state: controller.loadState,
              child: Column(
                children: [
                  Expanded(child: _buildView().marginAll(AppTheme.margin)),
                  _buildSubmitButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStudyFirstSwitch() {
    return Scell(
      title: const Text("优先新学"),
      // value: controller.user.name,
      showArrow: false,
      value: CustomSwitch(
        value: controller.bookSchedule.studyFirst,
        onChanged: (value) {
          controller.onChangeStudyFirst(value);
        },
      ),
    );
  }

  Widget _buildActiveSwitch() {
    return Scell(
      title: const Text("是否激活"),
      // value: controller.user.name,
      showArrow: false,
      value: CustomSwitch(
        value: controller.bookSchedule.active,
        onChanged: (value) {
          controller.onChangeActive(value);
        },
      ),
    );
  }

  _buildSubmitButton() {
    return Sbutton(
      onPressed: controller.onSubmit,
      shape: SbuttonShape.outline,
      width: double.infinity,
      child: const Text("提交"),
    );
  }
}
