part of widgets;

class <PERSON><PERSON><PERSON>ield extends StatefulWidget {
  final dynamic image;
  final String lable;
  final Function() onUpload;
  final Function() onDelete;
  const SimageField({
    super.key,
    required this.image,
    required this.lable,
    required this.onUpload,
    required this.onDelete,
  });

  @override
  State<SimageField> createState() => _SimageFieldState();
}

class _SimageFieldState extends State<SimageField> {
  @override
  Widget build(BuildContext context) {
    Widget item;
    if (widget.image != null) {
      item = _buildImageWithDeleteButton();
    } else {
      item = SinwellIconButton(onTab: () => widget.onUpload.call());
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.lable,
          style: TextStyle(
            fontSize: FontSize.body,
            fontWeight: FontWeight.bold,
          ),
        ).marginOnly(left: 8),
        const Sized<PERSON>ox(height: 8),
        LayoutBuilder(builder: (context, boxConstraints) {
          return Container(
            width: boxConstraints.maxWidth,
            color: Colors.grey[50],
            child: UnconstrainedBox(child: item),
          );
        })
      ],
    );
  }

  Widget _buildImageWithDeleteButton() {
    return SimageWithDeleteButton(
      image: widget.image,
      onDelete: widget.onDelete,
    );
  }
}

class SimageWithDeleteButton extends StatelessWidget {
  final dynamic image;
  final VoidCallback onDelete;
  final double width;
  final double height;

  const SimageWithDeleteButton({
    Key? key,
    required this.image,
    required this.onDelete,
    this.width = 120,
    this.height = 120,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Simage(
          width: width,
          height: height,
          url: image is File ? image.path : image ?? "",
          fit: BoxFit.contain,
        ),
        Positioned(
          right: 0,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white
                  .withValues(red: 0, green: 0, blue: 0, alpha: 0.7),
              shape: BoxShape.circle,
            ),
            child: InkWell(
              onTap: onDelete,
              child: const Icon(Icons.delete, color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }
}

class SAudioWithDeleteButton extends StatelessWidget {
  final String? audioPath;
  final VoidCallback onDelete;
  final double width;
  final double height;
  final Function() onTap;

  const SAudioWithDeleteButton({
    Key? key,
    required this.audioPath,
    required this.onDelete,
    required this.onTap,
    this.width = 120,
    this.height = 120,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GetBuilder<OxAudioController>(
            init: OxAudioController(),
            builder: (controller) {
              return InkWell(
                onTap: onTap,
                child: Sbutton.icon(
                  icon: Icon(
                    Icons.audio_file,
                    size: width,
                  ),
                ),
              );
            }),
        Positioned(
          right: 0,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white
                  .withValues(red: 0, green: 0, blue: 0, alpha: 0.7),
              shape: BoxShape.circle,
            ),
            child: InkWell(
              onTap: onDelete,
              child: const Icon(Icons.delete, color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }
}
