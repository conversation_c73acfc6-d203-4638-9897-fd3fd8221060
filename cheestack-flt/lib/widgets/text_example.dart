import 'package:flutter/material.dart';
import 'package:cheestack_flt/widgets/index.dart';

/// Text 组件使用示例
/// 
/// 这个示例展示了如何使用重构后的 Text 组件，
/// 该组件现在完全基于 Material 3 设计规范和主题系统。
class OxTextExample extends StatelessWidget {
  const OxTextExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Text 组件示例'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 显示文本样式
            _buildSection(
              '显示文本样式 (Display)',
              [
                const Text(
                  '显示大文本',
                  style: OxTextStyle.displayLarge,
                ),
                const SizedBox(height: 8),
                const Text(
                  '显示中文本',
                  style: OxTextStyle.displayMedium,
                ),
                const SizedBox(height: 8),
                const Text(
                  '显示小文本',
                  style: OxTextStyle.displaySmall,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 标题文本样式
            _buildSection(
              '标题文本样式 (Headline)',
              [
                const Text(
                  '大标题文本',
                  style: OxTextStyle.headlineLarge,
                ),
                const SizedBox(height: 8),
                const Text(
                  '中标题文本',
                  style: OxTextStyle.headlineMedium,
                ),
                const SizedBox(height: 8),
                const Text(
                  '小标题文本',
                  style: OxTextStyle.headlineSmall,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 标题样式
            _buildSection(
              '标题样式 (Title)',
              [
                const Text(
                  '大标题',
                  style: OxTextStyle.titleLarge,
                ),
                const SizedBox(height: 8),
                const Text(
                  '中标题',
                  style: OxTextStyle.titleMedium,
                ),
                const SizedBox(height: 8),
                const Text(
                  '小标题',
                  style: OxTextStyle.titleSmall,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 正文样式
            _buildSection(
              '正文样式 (Body)',
              [
                const Text(
                  '这是大正文文本，适用于重要的内容段落。它使用了主题中定义的 bodyLarge 样式，确保在不同主题下都有良好的可读性。',
                  style: OxTextStyle.bodyLarge,
                ),
                const SizedBox(height: 8),
                const Text(
                  '这是中正文文本，是最常用的文本样式，适用于大部分内容。它使用了主题中定义的 bodyMedium 样式。',
                  style: OxTextStyle.bodyMedium,
                ),
                const SizedBox(height: 8),
                const Text(
                  '这是小正文文本，适用于次要信息或说明性文本。它使用了主题中定义的 bodySmall 样式。',
                  style: OxTextStyle.bodySmall,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 标签样式
            _buildSection(
              '标签样式 (Label)',
              [
                const Text(
                  '大标签文本',
                  style: OxTextStyle.labelLarge,
                ),
                const SizedBox(height: 8),
                const Text(
                  '中标签文本',
                  style: OxTextStyle.labelMedium,
                ),
                const SizedBox(height: 8),
                const Text(
                  '小标签文本',
                  style: OxTextStyle.labelSmall,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 自定义属性示例
            _buildSection(
              '自定义属性示例',
              [
                const Text(
                  '自定义颜色文本',
                  style: OxTextStyle.bodyLarge,
                  color: Colors.red,
                ),
                const SizedBox(height: 8),
                const Text(
                  '自定义字体大小文本',
                  fontSize: 24.0,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 8),
                const Text(
                  '居中对齐的文本',
                  style: OxTextStyle.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  '带下划线的文本',
                  style: OxTextStyle.bodyMedium,
                  decoration: TextDecoration.underline,
                ),
                const SizedBox(height: 8),
                const SizedBox(
                  width: 200,
                  child: Text(
                    '这是一段很长的文本，用来演示文本溢出处理和最大行数限制功能',
                    style: OxTextStyle.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 默认样式示例
            _buildSection(
              '默认样式示例',
              [
                const Text(
                  '这是使用默认样式的文本，会自动使用主题中的 bodyMedium 样式',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }
}

/// 使用指南：
/// 
/// 1. 基本使用：
///    ```dart
///    Text('Hello World')  // 使用默认的 bodyMedium 样式
///    ```
/// 
/// 2. 使用预定义样式：
///    ```dart
///    Text('标题文本', style: OxTextStyle.titleLarge)
///    Text('正文文本', style: OxTextStyle.bodyMedium)
///    ```
/// 
/// 3. 自定义属性：
///    ```dart
///    Text(
///      '自定义文本',
///      style: OxTextStyle.bodyLarge,
///      color: Colors.red,
///      fontSize: 20.0,
///      fontWeight: FontWeight.bold,
///      textAlign: TextAlign.center,
///      decoration: TextDecoration.underline,
///      maxLines: 2,
///      overflow: TextOverflow.ellipsis,
///    )
///    ```
/// 
/// 4. 主题适配：
///    组件会自动适配当前主题（浅色/深色），无需额外配置
/// 
/// 5. 响应式设计：
///    字体大小会根据主题配置自动适配不同屏幕尺寸
/// 
/// 6. Material 3 兼容：
///    所有样式都基于 Material 3 设计规范，确保设计一致性
