import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';

import '../controllers/creation_controller.dart';
import 'widgets/sync_status_indicator.dart';
import 'widgets/responsive_layout.dart';

/// 创作主页面 - 展示创作统计和快捷入口
/// 采用本地优先架构，支持响应式设计
class CreationHomePage extends StatefulWidget {
  const CreationHomePage({super.key});

  @override
  State<CreationHomePage> createState() => _CreationHomePageState();
}

class _CreationHomePageState extends State<CreationHomePage>
    with AutomaticKeepAliveClientMixin {
  late CreationController controller;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    if (!Get.isRegistered<CreationController>()) {
      Get.put<CreationController>(CreationController());
    }
    controller = Get.find<CreationController>();
    controller.loadCreationStats();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<CreationController>(
      builder: (controller) {
        return Scaffold(
          appBar: _buildAppBar(),
          body: SafeArea(
            child: _buildHomeView(),
          ),
        );
      },
    );
  }

  // 创作主页面视图 - 使用响应式布局
  Widget _buildHomeView() {
    return ResponsiveLayout(
      mobile: _buildMobileLayout(),
      tablet: _buildTabletLayout(),
      desktop: _buildDesktopLayout(),
    );
  }

  // 手机布局
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: ResponsiveSpacing.getPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 同步状态指示器
          const SyncStatusIndicator(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 统计卡片
          _buildStatsCard(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 快捷操作
          _buildQuickActions(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 最近创作
          _buildRecentCreationsCard(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context, desktop: 32)),
        ],
      ),
    );
  }

  // 平板布局
  Widget _buildTabletLayout() {
    return SingleChildScrollView(
      padding: ResponsiveSpacing.getPadding(context),
      child: Column(
        children: [
          // 同步状态指示器
          const SyncStatusIndicator(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 上半部分：统计和快捷操作
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 2, child: _buildStatsCard()),
              SizedBox(width: ResponsiveSpacing.getSpacing(context)),
              Expanded(flex: 1, child: _buildQuickActions()),
            ],
          ),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 下半部分：最近创作
          _buildRecentCreationsCard(),
        ],
      ),
    );
  }

  // 桌面布局
  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: ResponsiveSpacing.getPadding(context),
      child: Column(
        children: [
          // 同步状态指示器
          const SyncStatusIndicator(),
          SizedBox(height: ResponsiveSpacing.getSpacing(context)),

          // 三列布局
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(flex: 2, child: _buildStatsCard()),
              SizedBox(width: ResponsiveSpacing.getSpacing(context)),
              Expanded(flex: 1, child: _buildQuickActions()),
              SizedBox(width: ResponsiveSpacing.getSpacing(context)),
              Expanded(flex: 2, child: _buildRecentCreationsCard()),
            ],
          ),
        ],
      ),
    );
  }

  // 统计卡片 - 显示创作统计数据
  Widget _buildStatsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: ResponsiveSpacing.getPadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '创作统计',
              style: TextStyle(
                fontSize: ResponsiveFontSize.getTitle(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getSpacing(context)),

            // 统计数据网格
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: Breakpoints.isMobile(context) ? 2 : 4,
              childAspectRatio: 1.5,
              crossAxisSpacing: ResponsiveSpacing.getSpacing(context),
              mainAxisSpacing: ResponsiveSpacing.getSpacing(context),
              children: [
                _buildStatItem('书籍总数', controller.totalBooks.toString(),
                    Icons.book_outlined),
                _buildStatItem('卡片总数', controller.totalCards.toString(),
                    Icons.style_outlined),
                _buildStatItem('今日创作', controller.todayCreations.toString(),
                    Icons.today_outlined),
                _buildStatItem('本周创作', controller.weekCreations.toString(),
                    Icons.date_range_outlined),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 统计项目
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(ResponsiveSpacing.getSpacing(context)),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: ResponsiveFontSize.getTitle(context),
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveFontSize.getTitle(context),
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveFontSize.getBody(context),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 快捷操作
  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: ResponsiveSpacing.getPadding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '快捷操作',
              style: TextStyle(
                fontSize: ResponsiveFontSize.getTitle(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: ResponsiveSpacing.getSpacing(context)),

            // 操作按钮
            _buildActionButton(
              '创建书籍',
              Icons.add_box_outlined,
              () => controller.toBookCreationPage(),
            ),
            SizedBox(height: ResponsiveSpacing.getSpacing(context, mobile: 8)),
            _buildActionButton(
              '创建卡片',
              Icons.add_card_outlined,
              () => controller.toCardCreationPage(),
            ),
            SizedBox(height: ResponsiveSpacing.getSpacing(context, mobile: 8)),
            _buildActionButton(
              '管理书籍',
              Icons.library_books_outlined,
              () => controller.toCreationListPage(),
            ),
            SizedBox(height: ResponsiveSpacing.getSpacing(context, mobile: 8)),
            _buildActionButton(
              '同步数据',
              Icons.sync_outlined,
              () => controller.syncToCloud(),
              isLoading: controller.isSyncing,
            ),
          ],
        ),
      ),
    );
  }

  // 操作按钮
  Widget _buildActionButton(String label, IconData icon, VoidCallback onTap,
      {bool isLoading = false}) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: isLoading ? null : onTap,
        icon: isLoading
            ? SizedBox(
                width: 16.sp,
                height: 16.sp,
                child: const CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(icon, size: 18.sp),
        label: Text(label),
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: ResponsiveSpacing.getSpacing(context),
            vertical: ResponsiveSpacing.getSpacing(context, mobile: 12),
          ),
          alignment: Alignment.centerLeft,
        ),
      ),
    );
  }

  // 书籍管理卡片
  Widget _buildBookManagementCard() {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => controller.toBookCreationPage(),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: EdgeInsets.all(AppTheme.paddingLarge.left),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Row(
            children: [
              // 图标区域
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.2),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
                child: Icon(
                  Icons.library_books_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 32.sp,
                ),
              ),
              SizedBox(width: AppTheme.paddingMedium.left),
              // 内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '书籍管理',
                      style: TextStyle(
                        fontSize: AppTheme.fontLarge,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    // 统计信息
                    Text(
                      '${controller.totalBooks} 本书籍',
                      style: TextStyle(
                        fontSize: AppTheme.fontTitle,
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              // 箭头图标
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.primary,
                size: 20.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 卡片管理卡片
  Widget _buildCardManagementCard() {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => controller.toCardCreationPage(),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Container(
          padding: EdgeInsets.all(AppTheme.paddingLarge.left),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Row(
            children: [
              // 图标区域
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .secondary
                      .withValues(alpha: 0.2),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
                child: Icon(
                  Icons.style_outlined,
                  color: Theme.of(context).colorScheme.secondary,
                  size: 32.sp,
                ),
              ),
              SizedBox(width: AppTheme.paddingMedium.left),
              // 内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '卡片管理',
                      style: TextStyle(
                        fontSize: AppTheme.fontLarge,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    // 统计信息
                    Text(
                      '${controller.totalCards} 张卡片',
                      style: TextStyle(
                        fontSize: AppTheme.fontTitle,
                        color: Theme.of(context).colorScheme.secondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              // 箭头图标
              Icon(
                Icons.arrow_forward_ios,
                color: Theme.of(context).colorScheme.secondary,
                size: 20.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 最近创作卡片
  Widget _buildRecentCreationsCard() {
    return Card(
      elevation: 4,
      child: Container(
        padding: EdgeInsets.all(AppTheme.paddingLarge.left),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题区域
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .tertiary
                        .withValues(alpha: 0.2),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusMedium),
                  ),
                  child: Icon(
                    Icons.history,
                    color: Theme.of(context).colorScheme.tertiary,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: AppTheme.paddingMedium.left),
                Expanded(
                  child: Text(
                    '最新创作',
                    style: TextStyle(
                      fontSize: AppTheme.fontLarge,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ),
                ),
                if (controller.recentCreations.isNotEmpty)
                  TextButton(
                    onPressed: () => controller.toCreationListPage(),
                    child: Text(
                      '查看全部',
                      style: TextStyle(
                        fontSize: AppTheme.fontSmall,
                        color: Theme.of(context).colorScheme.tertiary,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: AppTheme.paddingMedium.left),
            // 内容区域
            controller.recentCreations.isEmpty
                ? _buildEmptyRecentCreations()
                : _buildRecentCreationsList(),
          ],
        ),
      ),
    );
  }

  // 空的最近创作
  Widget _buildEmptyRecentCreations() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: AppTheme.paddingLarge.left),
      child: Column(
        children: [
          Icon(
            Icons.create_outlined,
            size: 48.sp,
            color: Theme.of(context)
                .colorScheme
                .onSurfaceVariant
                .withValues(alpha: 0.5),
          ),
          SizedBox(height: AppTheme.paddingSmall.left),
          Text(
            '还没有创作卡片',
            style: TextStyle(
              fontSize: AppTheme.fontTitle,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            '开始创作您的第一张学习卡片吧！',
            style: TextStyle(
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context)
                  .colorScheme
                  .onSurfaceVariant
                  .withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 最近创作列表 - 只显示3条卡片
  Widget _buildRecentCreationsList() {
    final displayCount = controller.recentCreations.length > 3
        ? 3
        : controller.recentCreations.length;

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: displayCount,
      separatorBuilder: (context, index) => Divider(
        height: 1.h,
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
      ),
      itemBuilder: (context, index) {
        final item = controller.recentCreations[index];

        return ListTile(
          contentPadding: EdgeInsets.symmetric(vertical: 4.h),
          leading: Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .secondary
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
            ),
            child: Icon(
              Icons.credit_card,
              color: Theme.of(context).colorScheme.secondary,
              size: 20.sp,
            ),
          ),
          title: Text(
            item['title'] ?? '',
            style: TextStyle(
              fontSize: AppTheme.fontTitle,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            item['subtitle'] ?? '',
            style: TextStyle(
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                item['time'] ?? '',
                style: TextStyle(
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurfaceVariant
                      .withValues(alpha: 0.7),
                ),
              ),
              SizedBox(height: 2.h),
              Icon(
                Icons.arrow_forward_ios,
                size: 12.sp,
                color: Theme.of(context)
                    .colorScheme
                    .onSurfaceVariant
                    .withValues(alpha: 0.5),
              ),
            ],
          ),
          onTap: () => controller.openRecentCreation(item),
        );
      },
    );
  }

  // 应用栏
  AppBar _buildAppBar() {
    return AppBar(
      title: const Text('创作中心'),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => controller.manualRefresh(),
        ),
        IconButton(
          icon: const Icon(Icons.bug_report),
          onPressed: () => controller.debugPrintStatus(),
        ),
        IconButton(
          icon: const Icon(Icons.settings_outlined),
          onPressed: () => controller.toCreationSettings(),
        ),
      ],
    );
  }
}
