import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/widgets/index.dart';

/// 增强版书籍卡片组件
/// 显示书籍封面、标题、简介、卡片数量、学习进度和快速操作菜单
/// 支持同步状态显示
class EnhancedBookCard extends StatelessWidget {
  final BookModel book;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onDuplicate;
  final VoidCallback? onManageCards;
  final int cardCount;
  final double studyProgress;
  final bool showSyncStatus;

  const EnhancedBookCard({
    super.key,
    required this.book,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onDuplicate,
    this.onManageCards,
    this.cardCount = 0,
    this.studyProgress = 0.0,
    this.showSyncStatus = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              // 封面图片
              _buildCover(context),
              SizedBox(width: 16.w),

              // 书籍信息
              Expanded(child: _buildBookInfo(context)),

              // 操作菜单
              _buildActionMenu(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建封面图片
  Widget _buildCover(BuildContext context) {
    return Container(
      width: 60.w,
      height: 80.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: book.cover != null && book.cover!.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: book.cover!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildCoverPlaceholder(context),
                errorWidget: (context, url, error) =>
                    _buildCoverPlaceholder(context),
              )
            : _buildCoverPlaceholder(context),
      ),
    );
  }

  /// 构建封面占位符
  Widget _buildCoverPlaceholder(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          ],
        ),
      ),
      child: Icon(
        Icons.book_outlined,
        size: 24.sp,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  /// 构建书籍信息
  Widget _buildBookInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 书籍标题
        Text(
          book.name ?? '未命名书籍',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),

        SizedBox(height: 4.h),

        // 书籍简介
        if (book.brief != null && book.brief!.isNotEmpty) ...[
          Text(
            book.brief!,
            style: TextStyle(
              fontSize: 14.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8.h),
        ],

        // 统计信息
        Row(
          children: [
            // 卡片数量
            _buildStatChip(
              context,
              icon: Icons.style_outlined,
              label: '$cardCount 张卡片',
            ),

            SizedBox(width: 8.w),

            // 隐私状态
            _buildPrivacyChip(context),
          ],
        ),

        SizedBox(height: 8.h),

        // 学习进度
        if (studyProgress > 0) ...[
          Row(
            children: [
              Expanded(
                child: LinearProgressIndicator(
                  value: studyProgress,
                  backgroundColor:
                      Theme.of(context).colorScheme.surfaceContainerHighest,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '${(studyProgress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(
    BuildContext context, {
    required IconData icon,
    required String label,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建隐私状态芯片
  Widget _buildPrivacyChip(BuildContext context) {
    final isPublic = book.privacy == 'public';
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isPublic
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPublic ? Icons.public : Icons.lock_outline,
            size: 14.sp,
            color: isPublic
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4.w),
          Text(
            isPublic ? '公开' : '私有',
            style: TextStyle(
              fontSize: 12.sp,
              color: isPublic
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作菜单
  Widget _buildActionMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'duplicate':
            onDuplicate?.call();
            break;
          case 'manage_cards':
            onManageCards?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit_outlined, size: 18.sp),
              SizedBox(width: 8.w),
              const Text('编辑'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(Icons.copy_outlined, size: 18.sp),
              SizedBox(width: 8.w),
              const Text('复制'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'manage_cards',
          child: Row(
            children: [
              Icon(Icons.style_outlined, size: 18.sp),
              SizedBox(width: 8.w),
              const Text('管理卡片'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                Icons.delete_outline,
                size: 18.sp,
                color: Theme.of(context).colorScheme.error,
              ),
              SizedBox(width: 8.w),
              Text(
                '删除',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
