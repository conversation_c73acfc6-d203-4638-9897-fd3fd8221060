import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/i18n/index.dart';
import '../../controllers/profile_controller.dart';

/// 密码强度指示器组件
/// 显示密码强度和相关要求
class PasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final PasswordStrength strength;
  final Map<String, bool> requirements;

  const PasswordStrengthIndicator({
    Key? key,
    required this.password,
    required this.strength,
    required this.requirements,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 强度标签和进度条
        Row(
          children: [
            Text(
              LocaleKeys.securityPasswordStrength.tr,
              fontSize: AppTheme.fontBody,
              color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 8.w),
            Text(
              _getStrengthText(),
              fontSize: AppTheme.fontBody,
              color: _getStrengthColor(),
              fontWeight: FontWeight.w600,
            ),
            const Spacer(),
            _buildStrengthIcon(),
          ],
        ),
        SizedBox(height: 8.h),

        // 强度进度条
        _buildStrengthBar(),
        SizedBox(height: 16.h),

        // 密码要求
        _buildRequirements(),
      ],
    );
  }

  /// 构建强度进度条
  Widget _buildStrengthBar() {
    double progress = 0.0;
    Color color = Colors.red;

    switch (strength) {
      case PasswordStrength.weak:
        progress = 0.33;
        color = Colors.red;
        break;
      case PasswordStrength.medium:
        progress = 0.66;
        color = Colors.orange;
        break;
      case PasswordStrength.strong:
        progress = 1.0;
        color = Colors.green;
        break;
    }

    return Container(
      height: 4.h,
      decoration: BoxDecoration(
        color: Theme.of(Get.context!).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(2.r),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress,
        child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2.r),
          ),
        ),
      ),
    );
  }

  /// 构建强度图标
  Widget _buildStrengthIcon() {
    IconData icon;
    Color color;

    switch (strength) {
      case PasswordStrength.weak:
        icon = Icons.warning_outlined;
        color = Colors.red;
        break;
      case PasswordStrength.medium:
        icon = Icons.info_outlined;
        color = Colors.orange;
        break;
      case PasswordStrength.strong:
        icon = Icons.check_circle_outlined;
        color = Colors.green;
        break;
    }

    return Icon(
      icon,
      size: 16.w,
      color: color,
    );
  }

  /// 构建密码要求列表
  Widget _buildRequirements() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.securityPasswordRequirements.tr,
          fontSize: AppTheme.fontBody,
          fontWeight: FontWeight.w600,
        ),
        SizedBox(height: 8.h),
        _buildRequirementItem(
          LocaleKeys.securityPasswordMinLength.tr,
          requirements['minLength'] ?? false,
        ),
        SizedBox(height: 4.h),
        _buildRequirementItem(
          LocaleKeys.securityPasswordContainNumber.tr,
          requirements['hasNumber'] ?? false,
        ),
        SizedBox(height: 4.h),
        _buildRequirementItem(
          LocaleKeys.securityPasswordContainLetter.tr,
          requirements['hasLetter'] ?? false,
        ),
        SizedBox(height: 4.h),
        _buildRequirementItem(
          LocaleKeys.securityPasswordContainSpecial.tr,
          requirements['hasSpecial'] ?? false,
        ),
      ],
    );
  }

  /// 构建单个要求项
  Widget _buildRequirementItem(String text, bool isValid) {
    return Row(
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.radio_button_unchecked,
          size: 14.w,
          color: isValid
              ? Colors.green
              : Theme.of(Get.context!).colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 8.w),
        Text(
          text,
          fontSize: AppTheme.fontSmall,
          color: isValid
              ? Colors.green
              : Theme.of(Get.context!).colorScheme.onSurfaceVariant,
        ),
      ],
    );
  }

  /// 获取强度文本
  String _getStrengthText() {
    switch (strength) {
      case PasswordStrength.weak:
        return LocaleKeys.securityPasswordWeak.tr;
      case PasswordStrength.medium:
        return LocaleKeys.securityPasswordMedium.tr;
      case PasswordStrength.strong:
        return LocaleKeys.securityPasswordStrong.tr;
    }
  }

  /// 获取强度颜色
  Color _getStrengthColor() {
    switch (strength) {
      case PasswordStrength.weak:
        return Colors.red;
      case PasswordStrength.medium:
        return Colors.orange;
      case PasswordStrength.strong:
        return Colors.green;
    }
  }
}
