import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/theme.dart';

/// 安全功能项卡片组件
/// 用于显示各种安全功能的统一卡片样式
class SecurityItemCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? value;
  final bool showArrow;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Widget? trailing;

  const SecurityItemCard({
    Key? key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.value,
    this.showArrow = true,
    this.onTap,
    this.iconColor,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: AppTheme.paddingMedium,
        decoration: BoxDecoration(
          color: Theme.of(Get.context!).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Theme.of(Get.context!).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: (iconColor ?? Theme.of(Get.context!).colorScheme.primary)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: iconColor ?? Theme.of(Get.context!).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),

            // 内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    fontSize: AppTheme.fontTitle,
                    fontWeight: FontWeight.w600,
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      subtitle!,
                      fontSize: AppTheme.fontSmall,
                      color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
                    ),
                  ],
                ],
              ),
            ),

            // 右侧内容
            if (trailing != null)
              trailing!
            else if (value != null) ...[
              Text(
                value!,
                fontSize: AppTheme.fontBody,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
              if (showArrow) SizedBox(width: 8.w),
            ],

            // 箭头
            if (showArrow)
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Theme.of(Get.context!).colorScheme.onSurfaceVariant,
              ),
          ],
        ),
      ),
    );
  }
}
